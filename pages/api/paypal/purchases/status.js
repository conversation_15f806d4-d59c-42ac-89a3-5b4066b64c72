// Get PayPal Purchase Status API
export const runtime = 'edge';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { orderId } = req.query;

    if (!orderId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Order ID is required' 
      });
    }

    // Get backend URL from environment
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';
    
    // Forward request to backend
    const response = await fetch(`${backendUrl}/api/paypal/purchases/status?orderId=${orderId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    const data = await response.json();
    const statusCode = response.status;

    if (!response.ok) {
      return res.status(statusCode).json(data);
    }

    return res.status(200).json(data);

  } catch (error) {
    console.error('PayPal purchase status error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      details: error.message 
    });
  }
}