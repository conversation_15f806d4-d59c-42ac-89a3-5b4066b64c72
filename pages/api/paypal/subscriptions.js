// PayPal Subscription Creation API
export const runtime = 'edge';

export default async function handler(req, res) {
  console.log('PayPal Subscriptions API called');
  console.log('Method:', req.method);
  console.log('Body:', req.body);

  if (req.method !== 'POST') {
    console.log('Method not allowed:', req.method);
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { tier, email, addons } = req.body;
    console.log('Extracted data:', { tier, email, addons });

    // Validate required fields
    if (!tier || !email) {
      console.log('Validation failed - missing tier or email');
      return res.status(400).json({ 
        success: false, 
        message: 'Tier and email are required' 
      });
    }

    // Get backend URL from environment
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';
    console.log('Backend URL:', backendUrl);
    
    const payload = {
      tier: tier.toLowerCase(),
      email,
      addons: addons || {}
    };
    console.log('Sending to backend:', payload);

    // Forward request to backend
    const response = await fetch(`${backendUrl}/api/paypal/subscriptions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    });

    console.log('Backend response status:', response.status);
    console.log('Backend response ok:', response.ok);

    const data = await response.json();
    const statusCode = response.status;
    console.log('Backend response data:', data);

    if (!response.ok) {
      console.log('Backend error response:', data);
      return res.status(statusCode).json(data);
    }

    console.log('Sending success response to frontend');
    return res.status(200).json(data);

  } catch (error) {
    console.error('PayPal subscription creation error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      details: error.message 
    });
  }
}