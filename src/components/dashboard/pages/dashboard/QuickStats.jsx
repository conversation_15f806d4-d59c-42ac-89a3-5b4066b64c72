import { StatCard } from './StatCard'

export function QuickStats({ user, theme }) {
  const stats = [
    {
      title: 'Websites',
      value: user.accountDetails.limits.currentUsage.websites,
      // Option 1: Keep showing limit (1/10)
      total: user.accountDetails.limits.restrictions.websiteLimit,
      // Option 2: Only show current count (uncomment line below and comment line above)
      // total: null, // This will show just "1" without "/10"
      icon: 'pi pi-globe',
    },
    {
      title: 'Credits',
      value: user.metrics.totalCreditsUsed,
      total: user.accountDetails.limits.restrictions.creditLimit,
      icon: 'pi pi-ticket',
    },
    {
      title: 'Posts',
      value: user.metrics.totalScans,
      icon: 'pi pi-send',
    },
    {
      title: 'Plan',
      value: user.accountDetails.subscription.currentPlan.toLowerCase(),
      icon: 'pi pi-star',
    },
  ]

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat) => (
        <StatCard
          className={theme === 'dark' ? 'dark:bg-[#151821] dark:border-white/5' : 'bg-white border border-gray-200'}
          key={stat.title}
          theme={theme}
          title={stat.title}
          value={stat.value}
          total={stat.total}
          icon={stat.icon}
        />
      ))}
    </div>
  )
}
