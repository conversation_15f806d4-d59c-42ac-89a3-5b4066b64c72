'use client'

function classNames(...classes) {
  return classes.filter(Boolean).join(' ')
}

import { useState, useEffect } from 'react'
import { RadioGroup } from '@headlessui/react'
import { useTheme } from '@/contexts/ThemeProvider'
import { useSearchParams } from 'next/navigation'

function CreditCardIcon(props) {
  return (
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" {...props}>
      <rect x="2" y="5" width="20" height="14" rx="2" />
      <path d="M2 10h20" />
    </svg>
  )
}

function BankIcon(props) {
  return (
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" {...props}>
      <path d="M3 21h18M3 10h18M5 6l7-3 7 3M4 10v11m16-11v11" />
    </svg>
  )
}

function PayPalIcon(props) {
  return (
    <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.149-.595C20.39 4.565 19.65 3.6 18.48 2.84c-1.324-.86-3.214-1.295-5.619-1.295H5.998c-.026 0-.051.002-.076.005-.322.04-.6.25-.697.56L2.118 20.044c-.12.375.076.756.463.756h4.606l1.12-7.106c.082-.518.526-.9 1.05-.9h2.19c4.298 0 7.664-1.747 8.647-6.797.03-.149.054-.294.077-.437z" />
    </svg>
  )
}

function ApplePayIcon(props) {
  return (
    <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701" />
    </svg>
  )
}

function GooglePayIcon(props) {
  return (
    <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M12.24 10.285V14.4h6.806c-.275 1.765-2.056 5.174-6.806 5.174-4.095 0-7.439-3.389-7.439-7.574s3.345-7.574 7.439-7.574c2.33 0 3.891.989 4.785 1.849l3.254-3.138C18.189 1.186 15.479 0 12.24 0c-6.635 0-12 5.365-12 12s5.365 12 12 12c6.926 0 11.52-4.869 11.52-11.726 0-.788-.085-1.39-.189-1.989H12.24z" />
    </svg>
  )
}

function CryptoIcon(props) {
  return (
    <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zM9.5 7h1.75V5.5h1.5V7H14.5c1.105 0 2 .895 2 2v1c0 .738-.404 1.376-1 1.723.596.347 1 .985 1 1.723v1c0 1.105-.895 2-2 2h-1.75v1.5h-1.5V16.5H9.5c-1.105 0-2-.895-2-2v-5.5c0-1.105.895-2 2-2zm0 1.5c-.276 0-.5.224-.5.5v1.5h5c.276 0 .5-.224.5-.5V9c0-.276-.224-.5-.5-.5H9.5zm0 4c-.276 0-.5.224-.5.5V15c0 .276.224.5.5.5h5c.276 0 .5-.224.5-.5v-1.5c0-.276-.224-.5-.5-.5H9.5z" />
    </svg>
  )
}

// Plan pricing structure
const planPricing = {
  Basic: {
    subscription: {
      price: 15,
      duration: 'per month',
      description: 'Monthly billing',
    },
    purchase: {
      price: 180,
      duration: 'lifetime',
      description: 'One-time payment',
    },
  },
  Pro: {
    subscription: {
      price: 40,
      duration: 'for 6 months',
      description: 'Billed every 6 months',
    },
    purchase: {
      price: 480,
      duration: 'lifetime',
      description: 'One-time payment',
    },
  },
  Enterprise: {
    subscription: {
      price: 99,
      duration: 'per year',
      description: 'Annual billing',
    },
    purchase: {
      price: 1188,
      duration: 'lifetime',
      description: 'One-time payment',
    },
  },
}

// Indonesian Payment Method Icons
function QRISIcon(props) {
  return (
    <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M3 3h6v6H3V3zm2 2v2h2V5H5zM3 15h6v6H3v-6zm2 2v2h2v-2H5zM15 3h6v6h-6V3zm2 2v2h2V5h-2zM11 5h2v2h-2V5zM5 11h2v2H5v-2zM11 7h2v4h-2V7zM7 13h4v2H7v-2zM15 11h2v2h-2v-2zM13 13h2v2h-2v-2zM17 13h4v2h-4v-2zM15 15h2v2h-2v-2zM19 15h2v6h-2v-6zM17 17h2v2h-2v-2z" />
    </svg>
  )
}

function VirtualAccountIcon(props) {
  return (
    <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z" />
    </svg>
  )
}

function EWalletIcon(props) {
  return (
    <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
    </svg>
  )
}

const paymentMethods = [
  {
    id: 'qris',
    title: 'QRIS',
    description:
      'Scan QR code with any Indonesian e-wallet app (DANA, GoPay, OVO, etc.)',
    icon: QRISIcon,
    category: 'indonesian',
  },
  {
    id: 'va-bca',
    title: 'Virtual Account BCA',
    description: 'Transfer via BCA Virtual Account - instant confirmation',
    icon: VirtualAccountIcon,
    category: 'indonesian',
  },
  {
    id: 'va-mandiri',
    title: 'Virtual Account Mandiri',
    description: 'Transfer via Mandiri Virtual Account - instant confirmation',
    icon: VirtualAccountIcon,
    category: 'indonesian',
  },
  {
    id: 'va-bni',
    title: 'Virtual Account BNI',
    description: 'Transfer via BNI Virtual Account - instant confirmation',
    icon: VirtualAccountIcon,
    category: 'indonesian',
  },
  {
    id: 'gopay',
    title: 'GoPay',
    description: 'Pay directly with your GoPay wallet - instant and secure',
    icon: EWalletIcon,
    category: 'indonesian',
  },
  {
    id: 'dana',
    title: 'DANA',
    description: 'Pay directly with your DANA wallet - instant and secure',
    icon: EWalletIcon,
    category: 'indonesian',
  },
  {
    id: 'paypal',
    title: 'PayPal',
    description:
      'Fast and secure payment through your PayPal account or guest checkout',
    icon: PayPalIcon,
    category: 'international',
  },
  {
    id: 'credit-card',
    title: 'Credit Card',
    description:
      'Secure payment processing via encrypted credit or debit card transaction',
    icon: CreditCardIcon,
    category: 'international',
  },
  {
    id: 'apple-pay',
    title: 'Apple Pay',
    description:
      'Quick payment using Touch ID, Face ID, or your device passcode',
    icon: ApplePayIcon,
    category: 'international',
  },
  {
    id: 'google-pay',
    title: 'Google Pay',
    description: 'Seamless checkout with saved payment methods from Google',
    icon: GooglePayIcon,
    category: 'international',
  },
  {
    id: 'cryptocurrency',
    title: 'Cryptocurrency',
    description:
      'Pay with Bitcoin, Ethereum, or other supported cryptocurrencies',
    icon: CryptoIcon,
    category: 'international',
  },
  {
    id: 'bank-transfer',
    title: 'Bank Transfer',
    description:
      'Direct payment via secure bank wire transfer with enhanced verification',
    icon: BankIcon,
    category: 'international',
  },
]

export function PaymentContent() {
  const [selectedMethod, setSelectedMethod] = useState(paymentMethods[0])
  const [paymentType, setPaymentType] = useState('subscription') // 'subscription' or 'purchase'
  const [isProcessing, setIsProcessing] = useState(false)
  const [paymentStatus, setPaymentStatus] = useState('idle') // 'idle', 'processing', 'success', 'error', 'qr_ready', 'va_ready', 'ewallet_ready'
  const [paymentData, setPaymentData] = useState(null) // Store payment details for display
  const searchParams = useSearchParams()
  const plan = searchParams.get('plan')
  const { theme } = useTheme()

  // Get pricing for current plan
  const currentPlanPricing =
    plan && planPricing[plan] ? planPricing[plan] : null

  // Add useEffect to listen for PayPal callback messages
  useEffect(() => {
    const handleMessage = (event) => {
      // Only accept messages from our domain for security
      if (event.origin !== window.location.origin) {
        return
      }

      if (event.data.type === 'paypal_callback') {
        console.log('PayPal callback message received:', event.data)

        if (event.data.status === 'success') {
          console.log('💳 PayPal payment successful!')
          setPaymentStatus('success')
          setIsProcessing(false)

          // Show success message and redirect to dashboard after 3 seconds
          setTimeout(() => {
            window.location.href = '/dashboard'
          }, 3000)
        } else if (
          event.data.status === 'cancel' ||
          event.data.status === 'cancelled'
        ) {
          console.log('❌ PayPal payment cancelled')
          setPaymentStatus('idle')
          setIsProcessing(false)
        } else if (event.data.status === 'error') {
          console.log('❌ PayPal payment error')
          setPaymentStatus('error')
          setIsProcessing(false)
        }
      }

      // Handle Xendit callback messages
      if (event.data.type === 'xendit_callback') {
        console.log('Xendit callback message received:', event.data)

        if (event.data.status === 'success') {
          console.log('💳 Xendit payment successful via callback!')
          setPaymentStatus('success')
          setIsProcessing(false)

          // Show success message and redirect to dashboard after 3 seconds
          setTimeout(() => {
            window.location.href = '/dashboard'
          }, 3000)
        } else if (
          event.data.status === 'cancel' ||
          event.data.status === 'cancelled'
        ) {
          console.log('❌ Xendit payment cancelled')
          setPaymentStatus('idle')
          setIsProcessing(false)
        } else if (event.data.status === 'error') {
          console.log('❌ Xendit payment error')
          setPaymentStatus('error')
          setIsProcessing(false)
        }
      }
    }

    // Add event listener for postMessage
    window.addEventListener('message', handleMessage)

    // Cleanup event listener on unmount
    return () => {
      window.removeEventListener('message', handleMessage)
    }
  }, [])

  // Add useEffect to handle URL parameters for payment status
  useEffect(() => {
    const subscriptionStatus = searchParams.get('subscription_status')
    const purchaseStatus = searchParams.get('purchase_status')

    if (subscriptionStatus === 'success' || purchaseStatus === 'success') {
      console.log('💳 Payment success detected from URL parameters')
      console.log('🔍 URL Parameters detected:', {
        subscriptionStatus,
        purchaseStatus,
        subscriptionId: searchParams.get('subscription_id'),
        message: searchParams.get('message'),
        fullURL: window.location.href,
      })

      // Don't auto-redirect if payment already successful (webhook took priority)
      if (paymentStatus === 'success') {
        console.log(
          '🚫 Payment already successful via webhook, ignoring URL redirect',
        )
        return
      }

      setPaymentStatus('success')
      setIsProcessing(false)

      // Try to close any open modal windows (in case webhook didn't close them)
      console.log('🔍 Attempting to close any open payment modal windows...')
      try {
        // Check for windows with common payment window names
        const commonWindowNames = [
          'xendit-payment',
          'paypal-payment',
          'payment-window',
        ]
        commonWindowNames.forEach((windowName) => {
          console.log(`🔍 Checking for window: ${windowName}`)
          // Note: Due to browser security, we can't directly access other windows
          // But we can try to send a message to close them
        })

        // Send a broadcast message to any listening payment windows
        window.postMessage(
          {
            type: 'payment_success_close_modal',
            source: 'payment_page',
            timestamp: Date.now(),
          },
          window.location.origin,
        )

        console.log('📢 Sent close modal message to any listening windows')
      } catch (error) {
        console.error('❌ Error attempting to close modal windows:', error)
      }

      // Show success message and redirect to dashboard after 3 seconds
      console.log('🔄 Setting up redirect to dashboard in 3 seconds...')
      setTimeout(() => {
        console.log('🏠 Redirecting to dashboard...')
        window.location.href = '/dashboard'
      }, 3000)
    } else if (
      subscriptionStatus === 'cancelled' ||
      purchaseStatus === 'cancelled'
    ) {
      console.log('❌ Payment cancelled detected from URL parameters')
      setPaymentStatus('idle')
      setIsProcessing(false)
    } else if (subscriptionStatus === 'error' || purchaseStatus === 'error') {
      console.log('❌ Payment error detected from URL parameters')
      setPaymentStatus('error')
      setIsProcessing(false)
    }
  }, [searchParams, paymentStatus])

  // Handle Xendit payment
  const handleXenditPayment = async () => {
    if (!plan || !currentPlanPricing) {
      alert('Please select a plan first')
      return
    }

    setIsProcessing(true)
    setPaymentStatus('processing')

    let statusInterval = null // Declare statusInterval variable
    let eventSource = null // Declare eventSource variable
    let xenditWindow = null // Declare xenditWindow variable
    let checkClosed = null // Declare checkClosed variable

    try {
      // Get USD price from current plan pricing
      const usdAmount =
        paymentType === 'subscription'
          ? currentPlanPricing.subscription.price
          : currentPlanPricing.purchase.price

      // Map payment method IDs to backend format
      let payment_methods = []

      switch (selectedMethod.id) {
        case 'qris':
          payment_methods = ['QRIS']
          break
        case 'va-bca':
          payment_methods = ['VA_BCA']
          break
        case 'va-mandiri':
          payment_methods = ['VA_MANDIRI']
          break
        case 'va-bni':
          payment_methods = ['VA_BNI']
          break
        case 'gopay':
          payment_methods = ['GOPAY']
          break
        case 'dana':
          payment_methods = ['DANA']
          break
        default:
          throw new Error('Unsupported payment method')
      }

      // Payload sesuai dengan backend API format
      const payload = {
        userId: '<EMAIL>', // TODO: Get from user context
        tier: plan.toLowerCase(), // backend expects lowercase
        payment_methods: payment_methods,
        usd_amount: usdAmount, // Backend akan konversi USD ke IDR
        payment_type: paymentType, // subscription atau purchase
      }

      console.log('Initiating Xendit payment:', { payload })

      // Request melalui Next.js API proxy untuk menghindari CORS
      console.log(
        'Making request via proxy to:',
        '/api/proxy/xendit/subscriptions',
      )

      const xenditResponse = await fetch('/api/proxy/xendit/subscriptions', {
        method: 'POST',
        credentials: 'include', // Sertakan cookies untuk otentikasi
        headers: {
          accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      const data = await xenditResponse.json()
      console.log('Xendit API response:', data)

      if (data.success && data.data?.invoiceUrl) {
        // Sama seperti PayPal - buka window modal untuk semua payment method Indonesia
        const paymentData = data.data

        console.log('Opening Xendit checkout window:', paymentData.invoiceUrl)

        // Open Xendit checkout in new window
        xenditWindow = window.open(
          paymentData.invoiceUrl,
          'xendit-payment',
          'width=800,height=600,scrollbars=yes,resizable=yes',
        )

        // Debug window creation
        console.log('🔍 Window creation result:', {
          windowExists: !!xenditWindow,
          windowClosed: xenditWindow ? xenditWindow.closed : 'N/A',
          windowName: xenditWindow ? xenditWindow.name : 'N/A',
          windowLocation: xenditWindow ? 'accessible' : 'N/A',
        })

        // Store payment info for status checking - gunakan invoice_id untuk status check
        const trackingId =
          paymentData.invoice_id ||
          paymentData.referenceId ||
          paymentData.subscription_id ||
          paymentData.id

        console.log('🔍 Payment tracking info:', {
          invoice_id: paymentData.invoice_id,
          referenceId: paymentData.referenceId,
          subscription_id: paymentData.subscription_id,
          id: paymentData.id,
          selectedTrackingId: trackingId,
        })

        // Check if window is closed manually (user cancelled)
        checkClosed = setInterval(() => {
          if (
            xenditWindow &&
            xenditWindow.closed &&
            paymentStatus === 'processing'
          ) {
            console.log('🚪 Xendit window closed manually by user')
            console.log('🔍 Manual close detection - Window state:', {
              windowExists: !!xenditWindow,
              windowClosed: xenditWindow.closed,
              paymentStatus: paymentStatus,
            })
            setPaymentStatus('idle')
            setIsProcessing(false)
            clearInterval(checkClosed)
            if (eventSource) eventSource.close()
          }
        }, 1000)

        // Listen for webhook notifications via Server-Sent Events with reconnection
        console.log('🔌 Connecting to webhook stream...')
        let reconnectAttempts = 0
        const maxReconnectAttempts = 5

        const connectSSE = () => {
          eventSource = new EventSource('/api/xendit/webhook-stream')

          eventSource.onopen = () => {
            console.log('✅ SSE connection established')
            reconnectAttempts = 0 // Reset on successful connection
          }

          eventSource.onmessage = (event) => {
            try {
              const webhookData = JSON.parse(event.data)
              console.log('📨 Webhook notification received:', webhookData)

              // Skip heartbeat and connection messages
              if (
                webhookData.type === 'heartbeat' ||
                webhookData.type === 'connected'
              ) {
                console.log('💓 Heartbeat or connection message, skipping...')
                return
              }

              // Check if this webhook is for our payment - more flexible matching
              const isMatchingPayment =
                webhookData.external_id === trackingId ||
                webhookData.id === trackingId ||
                webhookData.invoice_id === trackingId ||
                webhookData.invoice_id === paymentData.invoice_id ||
                webhookData.external_id === paymentData.external_id ||
                webhookData.id === paymentData.id

              console.log('🔍 Comparing webhook with payment data:', {
                webhookExternalId: webhookData.external_id,
                webhookId: webhookData.id,
                webhookInvoiceId: webhookData.invoice_id,
                paymentInvoiceId: paymentData.invoice_id,
                paymentExternalId: paymentData.external_id,
                paymentId: paymentData.id,
                ourTrackingId: trackingId,
                isMatch: isMatchingPayment,
              })

              if (isMatchingPayment) {
                console.log('✅ Webhook matches our payment:', webhookData)

                // Debug window state before closing
                console.log('🔍 Window state before closing:', {
                  windowExists: !!xenditWindow,
                  windowClosed: xenditWindow ? xenditWindow.closed : 'N/A',
                  windowName: xenditWindow ? xenditWindow.name : 'N/A',
                })

                if (
                  webhookData.status === 'PAID' ||
                  webhookData.status === 'COMPLETED'
                ) {
                  console.log('💳 Payment successful via webhook!')
                  setPaymentStatus('success')
                  setIsProcessing(false)

                  // Close Xendit window immediately
                  if (xenditWindow && !xenditWindow.closed) {
                    console.log(
                      '🔒 Attempting to close Xendit window after successful payment',
                    )
                    try {
                      xenditWindow.close()
                      console.log('✅ Window close() called successfully')

                      // Check if window is actually closed
                      setTimeout(() => {
                        console.log('🔍 Window state after close:', {
                          windowExists: !!xenditWindow,
                          windowClosed: xenditWindow
                            ? xenditWindow.closed
                            : 'N/A',
                        })
                      }, 500)
                    } catch (error) {
                      console.error('❌ Error closing window:', error)
                    }
                  } else {
                    console.log('ℹ️ Window is null or already closed')
                  }

                  // Clear intervals and close connections
                  if (checkClosed) clearInterval(checkClosed)
                  eventSource.close()
                  clearInterval(statusInterval)

                  // Clean URL parameters to prevent conflict with URL parameter handler
                  console.log(
                    '🧹 Cleaning URL parameters to prevent auto-redirect...',
                  )
                  const cleanUrl = window.location.pathname
                  window.history.replaceState({}, '', cleanUrl)

                  // Redirect to dashboard after 3 seconds (webhook has higher priority)
                  setTimeout(() => {
                    console.log('🏠 Redirecting to dashboard via webhook...')
                    window.location.href = '/dashboard'
                  }, 3000)
                } else if (
                  webhookData.status === 'FAILED' ||
                  webhookData.status === 'EXPIRED' ||
                  webhookData.status === 'CANCELLED'
                ) {
                  console.log('❌ Payment failed via webhook')
                  setPaymentStatus('error')
                  setIsProcessing(false)

                  // Close Xendit window
                  if (xenditWindow && !xenditWindow.closed) {
                    console.log('🔒 Closing Xendit window after failed payment')
                    xenditWindow.close()
                  }

                  // Clear intervals and close connections
                  if (checkClosed) clearInterval(checkClosed)
                  eventSource.close()
                  clearInterval(statusInterval)
                }
              } else {
                console.log(
                  '🔍 Webhook does not match our payment, ignoring...',
                )
              }
            } catch (error) {
              console.error('💥 Error processing webhook data:', error)
            }
          }

          eventSource.onerror = (error) => {
            console.error('💥 SSE connection error:', error)

            // Try to reconnect if not too many attempts
            if (
              reconnectAttempts < maxReconnectAttempts &&
              paymentStatus === 'processing'
            ) {
              reconnectAttempts++
              console.log(
                `🔄 Attempting SSE reconnection ${reconnectAttempts}/${maxReconnectAttempts}`,
              )

              setTimeout(() => {
                if (paymentStatus === 'processing') {
                  connectSSE()
                }
              }, 2000 * reconnectAttempts) // Exponential backoff
            } else {
              console.log(
                '❌ Max SSE reconnection attempts reached or payment no longer processing',
              )
            }
          }
        }

        // Initial connection
        connectSSE()

        // Rely only on webhook for payment status updates
        console.log('🎯 Using webhook-only approach for payment status updates')

        // Cleanup after 10 minutes
        setTimeout(() => {
          if (eventSource) eventSource.close()
          if (checkClosed) clearInterval(checkClosed)
          clearInterval(statusInterval)
          if (xenditWindow && !xenditWindow.closed) {
            console.log('🔒 Force closing Xendit window after timeout')
            xenditWindow.close()
          }
          if (paymentStatus === 'processing') {
            setPaymentStatus('error')
            setIsProcessing(false)
          }
        }, 600000) // 10 minutes
      } else {
        throw new Error(data.message || 'Failed to create Xendit payment')
      }
    } catch (error) {
      console.error('Xendit payment error:', error)
      setPaymentStatus('error')
      setIsProcessing(false)

      // Cleanup intervals and connections
      if (statusInterval) {
        clearInterval(statusInterval)
      }

      // Close any open Xendit window
      if (
        typeof xenditWindow !== 'undefined' &&
        xenditWindow &&
        !xenditWindow.closed
      ) {
        console.log('🔒 Closing Xendit window due to error')
        xenditWindow.close()
      }

      alert('Payment failed: ' + error.message)
    }
  }

  // Handle PayPal payment
  const handlePayPalPayment = async () => {
    if (!plan || !currentPlanPricing) {
      alert('Please select a plan first')
      return
    }

    setIsProcessing(true)
    setPaymentStatus('processing')

    try {
      const amount =
        paymentType === 'subscription'
          ? currentPlanPricing.subscription.price
          : currentPlanPricing.purchase.price

      const endpoint =
        paymentType === 'subscription'
          ? '/api/paypal/subscriptions'
          : '/api/paypal/purchases'

      const payload =
        paymentType === 'subscription'
          ? {
              tier: plan,
              email: '<EMAIL>', // TODO: Get from user context
              addons: {},
            }
          : {
              tier: plan,
              email: '<EMAIL>', // TODO: Get from user context
              amount: amount,
              addons: {},
            }

      console.log('Initiating PayPal payment:', { endpoint, payload })

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      const data = await response.json()
      console.log('PayPal API response:', data)

      if (data.success && (data.approval_url || data.data?.approvalUrl)) {
        // Get the approval URL from either format
        const approvalUrl = data.approval_url || data.data?.approvalUrl

        // Open PayPal in new window
        const paypalWindow = window.open(
          approvalUrl,
          'paypal-payment',
          'width=800,height=600,scrollbars=yes,resizable=yes',
        )

        // Store payment info for webhook tracking
        const paymentId = data.data?.orderId || data.orderId
        console.log('PayPal payment initiated:', paymentId)
        console.log('🎯 PayPal window opened, waiting for callback...')

        // Check if window is closed manually (user cancelled)
        const checkClosed = setInterval(() => {
          if (paypalWindow.closed && paymentStatus === 'processing') {
            console.log('PayPal window closed manually')
            setPaymentStatus('idle')
            setIsProcessing(false)
            clearInterval(checkClosed)
          }
        }, 1000)

        // Cleanup after 10 minutes
        setTimeout(() => {
          clearInterval(checkClosed)
          if (paypalWindow && !paypalWindow.closed) {
            paypalWindow.close()
          }
          if (paymentStatus === 'processing') {
            setPaymentStatus('error')
            setIsProcessing(false)
          }
        }, 600000) // 10 minutes
      } else {
        throw new Error(data.message || 'Failed to create PayPal payment')
      }
    } catch (error) {
      console.error('PayPal payment error:', error)
      setPaymentStatus('error')
      setIsProcessing(false)
      alert('Payment failed: ' + error.message)
    }
  }

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault()

    if (selectedMethod.id === 'paypal') {
      handlePayPalPayment()
    } else if (selectedMethod.category === 'indonesian') {
      handleXenditPayment()
    } else {
      // Handle other payment methods
      alert(`Payment with ${selectedMethod.title} is not implemented yet`)
    }
  }

  console.log('Selected Method ID:', selectedMethod.id)
  console.log('Payment Type:', paymentType)
  console.log('Plan:', plan)
  console.log('Current Plan Pricing:', currentPlanPricing)

  return (
    <div className="mx-auto max-w-full space-y-6">
      {/* Header */}
      <div
        className={`relative overflow-hidden rounded-md border border-gray-200 dark:border-white/5 ${theme === 'dark' ? 'bg-gradient-to-b from-[#0f1117] to-[#151821]' : 'bg-white'}`}
      >
        {/* Enhanced glow effect */}
        <div
          className={`absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent ${theme === 'dark' ? 'via-blue-500/30' : 'via-blue-300/50'} to-transparent`}
        />

        <div className={`px-6 py-4 ${theme === 'dark' ? '' : 'bg-white'}`}>
          <h1
            className={`font-mono text-lg ${theme === 'dark' ? 'text-blue-400' : 'text-blue-700'}`}
          >
            {plan ? 'Secure Payment Processing' : 'Manage Payment Methods'}
          </h1>
          <p
            className={`mt-1 font-mono text-xs ${theme === 'dark' ? 'text-gray-500' : 'text-gray-500'}`}
          >
            {plan ? (
              <>
                Complete your subscription for the{' '}
                <span
                  className={`text-blue-400 ${theme === 'dark' ? '' : 'text-blue-700'}`}
                >
                  {plan}
                </span>{' '}
                plan.
              </>
            ) : (
              'Add or update your payment methods for future use.'
            )}
          </p>
        </div>
      </div>

      {/* Plan & Payment Type Selection */}
      {plan && currentPlanPricing && (
        <div
          className={`relative overflow-hidden rounded-md border border-gray-200 dark:border-white/5 ${theme === 'dark' ? 'bg-gradient-to-b from-[#0f1117] to-[#151821]' : 'bg-white'}`}
        >
          <div
            className={`absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent ${theme === 'dark' ? 'via-blue-500/30' : 'via-blue-500/10'} to-transparent`}
          />

          <div className="p-6">
            <div
              className={`mb-4 flex items-center gap-2 ${theme === 'dark' ? '' : 'bg-white'}`}
            >
              <h2
                className={`font-mono text-sm ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'}`}
              >
                Choose Payment Type for {plan} Plan
              </h2>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {/* Subscription Option */}
              <div
                onClick={
                  !isProcessing
                    ? () => setPaymentType('subscription')
                    : undefined
                }
                className={`rounded border p-4 transition-all ${
                  isProcessing
                    ? 'cursor-not-allowed border-gray-300 bg-gray-100 opacity-50 dark:border-gray-600 dark:bg-gray-800'
                    : 'cursor-pointer'
                } ${
                  paymentType === 'subscription' && !isProcessing
                    ? `${theme === 'dark' ? 'border-blue-500/50 bg-blue-500/10' : 'border-blue-500/50 bg-blue-500/10'}`
                    : !isProcessing
                      ? `${theme === 'dark' ? 'border-blue-900/20 bg-[#151821]/50 hover:border-blue-500/30 hover:bg-blue-500/5' : 'border-gray-200 bg-white hover:border-blue-500/30 hover:bg-blue-500/5'}`
                      : ''
                }`}
              >
                <div className="mb-2 flex items-center justify-between">
                  <h3
                    className={`font-mono text-sm font-bold ${theme === 'dark' ? 'text-gray-300' : 'text-gray-800'}`}
                  >
                    Subscription
                  </h3>
                  <div
                    className={`h-4 w-4 rounded-full border-2 ${
                      paymentType === 'subscription'
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-gray-300'
                    }`}
                  >
                    {paymentType === 'subscription' && (
                      <div className="h-full w-full scale-50 rounded-full bg-white"></div>
                    )}
                  </div>
                </div>
                <div className="text-left">
                  <div className="flex items-baseline gap-1">
                    <span
                      className={`font-mono text-xl font-black ${theme === 'dark' ? 'text-gray-300' : 'text-gray-800'}`}
                    >
                      ${currentPlanPricing.subscription.price}
                    </span>
                    <span className="font-mono text-xs text-gray-500">
                      {currentPlanPricing.subscription.duration}
                    </span>
                  </div>
                  <p className="mt-1 font-mono text-xs text-gray-500">
                    {currentPlanPricing.subscription.description}
                  </p>
                </div>
              </div>

              {/* Purchase Option */}
              <div
                onClick={
                  !isProcessing ? () => setPaymentType('purchase') : undefined
                }
                className={`rounded border p-4 transition-all ${
                  isProcessing
                    ? 'cursor-not-allowed border-gray-300 bg-gray-100 opacity-50 dark:border-gray-600 dark:bg-gray-800'
                    : 'cursor-pointer'
                } ${
                  paymentType === 'purchase' && !isProcessing
                    ? `${theme === 'dark' ? 'border-blue-500/50 bg-blue-500/10' : 'border-blue-500/50 bg-blue-500/10'}`
                    : !isProcessing
                      ? `${theme === 'dark' ? 'border-blue-900/20 bg-[#151821]/50 hover:border-blue-500/30 hover:bg-blue-500/5' : 'border-gray-200 bg-white hover:border-blue-500/30 hover:bg-blue-500/5'}`
                      : ''
                }`}
              >
                <div className="mb-2 flex items-center justify-between">
                  <h3
                    className={`font-mono text-sm font-bold ${theme === 'dark' ? 'text-gray-300' : 'text-gray-800'}`}
                  >
                    Lifetime Purchase
                  </h3>
                  <div
                    className={`h-4 w-4 rounded-full border-2 ${
                      paymentType === 'purchase'
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-gray-300'
                    }`}
                  >
                    {paymentType === 'purchase' && (
                      <div className="h-full w-full scale-50 rounded-full bg-white"></div>
                    )}
                  </div>
                </div>
                <div className="text-left">
                  <div className="flex items-baseline gap-1">
                    <span
                      className={`font-mono text-xl font-black ${theme === 'dark' ? 'text-gray-300' : 'text-gray-800'}`}
                    >
                      ${currentPlanPricing.purchase.price}
                    </span>
                    <span className="font-mono text-xs text-gray-500">
                      {currentPlanPricing.purchase.duration}
                    </span>
                  </div>
                  <p className="mt-1 font-mono text-xs text-gray-500">
                    {currentPlanPricing.purchase.description}
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-4 rounded border border-gray-200 bg-gray-50 p-3 dark:border-white/5 dark:bg-[#151821]/50">
              <p className="font-mono text-xs text-gray-600 dark:text-gray-400">
                Selected:{' '}
                <span className="font-bold">
                  {paymentType === 'subscription'
                    ? 'Subscription'
                    : 'Lifetime Purchase'}{' '}
                  - $
                  {paymentType === 'subscription'
                    ? currentPlanPricing.subscription.price
                    : currentPlanPricing.purchase.price}
                  {paymentType === 'subscription'
                    ? ` ${currentPlanPricing.subscription.duration}`
                    : ' lifetime'}
                </span>
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Payment Content */}
      <div
        className={`relative overflow-hidden rounded-md border border-gray-200 dark:border-white/5 ${theme === 'dark' ? 'bg-gradient-to-b from-[#0f1117] to-[#151821]' : 'bg-white'}`}
      >
        {/* Enhanced glow effect */}
        <div
          className={`absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent ${theme === 'dark' ? 'via-blue-500/30' : 'via-blue-500/10'} to-transparent`}
        />

        <div className="p-6">
          <div className="space-y-6">
            {/* Payment Status Display */}
            {paymentStatus === 'processing' && (
              <div className="rounded border border-blue-200 bg-blue-50 p-4 dark:border-blue-500/20 dark:bg-blue-500/10">
                <div className="flex items-center gap-3">
                  <div className="h-6 w-6 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
                  <div>
                    <h3 className="font-mono text-sm font-bold text-blue-800 dark:text-blue-400">
                      {selectedMethod.category === 'indonesian'
                        ? 'Processing Xendit Payment'
                        : 'Processing PayPal Payment'}
                    </h3>
                    <p className="font-mono text-xs text-blue-600 dark:text-blue-300">
                      {selectedMethod.category === 'indonesian'
                        ? `Complete your payment in the ${selectedMethod.title} window. This page will update automatically.`
                        : 'Complete your payment in the PayPal window. This page will update automatically.'}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {paymentStatus === 'success' && (
              <div className="rounded border border-green-200 bg-green-50 p-4 dark:border-green-500/20 dark:bg-green-500/10">
                <div className="flex items-center gap-3">
                  <svg
                    className="h-6 w-6 text-green-600 dark:text-green-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <div>
                    <h3 className="font-mono text-sm font-bold text-green-800 dark:text-green-400">
                      Payment Successful!
                    </h3>
                    <p className="font-mono text-xs text-green-600 dark:text-green-300">
                      Your {plan} plan has been activated successfully.
                      {searchParams.get('subscription_id') && (
                        <span className="mt-1 block">
                          Subscription ID: {searchParams.get('subscription_id')}
                        </span>
                      )}
                      <span className="mt-1 block">
                        Redirecting to dashboard in 3 seconds...
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            )}

            {paymentStatus === 'error' && (
              <div className="rounded border border-red-200 bg-red-50 p-4 dark:border-red-500/20 dark:bg-red-500/10">
                <div className="flex items-center gap-3">
                  <svg
                    className="h-6 w-6 text-red-600 dark:text-red-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <div>
                    <h3 className="font-mono text-sm font-bold text-red-800 dark:text-red-400">
                      Payment Failed
                    </h3>
                    <p className="font-mono text-xs text-red-600 dark:text-red-300">
                      There was an issue processing your payment. Please try
                      again.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Debug Tools - Only show in development */}
            {process.env.NODE_ENV === 'development' &&
              paymentStatus === 'processing' && (
                <div className="rounded border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-500/20 dark:bg-yellow-500/10">
                  <h4 className="mb-2 font-mono text-sm font-bold text-yellow-800 dark:text-yellow-400">
                    Debug Tools
                  </h4>
                  <div className="flex gap-2">
                    <button
                      onClick={async () => {
                        try {
                          const response = await fetch(
                            '/api/xendit/webhook-listener',
                            {
                              method: 'POST',
                              headers: { 'Content-Type': 'application/json' },
                              body: JSON.stringify({
                                id: 'test-paid-' + Date.now(),
                                external_id: 'test-paid-' + Date.now(),
                                invoice_id: 'test-paid-' + Date.now(),
                                status: 'PAID',
                                type: 'invoice',
                              }),
                            },
                          )
                          console.log(
                            'Test webhook sent:',
                            await response.json(),
                          )
                        } catch (error) {
                          console.error('Test webhook error:', error)
                        }
                      }}
                      className="rounded bg-green-500 px-3 py-1 font-mono text-xs text-white hover:bg-green-600"
                    >
                      Test PAID Webhook
                    </button>
                    <button
                      onClick={async () => {
                        try {
                          const response = await fetch(
                            '/api/xendit/webhook-listener',
                            {
                              method: 'POST',
                              headers: { 'Content-Type': 'application/json' },
                              body: JSON.stringify({
                                id: 'test-failed-' + Date.now(),
                                external_id: 'test-failed-' + Date.now(),
                                invoice_id: 'test-failed-' + Date.now(),
                                status: 'FAILED',
                                type: 'invoice',
                              }),
                            },
                          )
                          console.log(
                            'Test webhook sent:',
                            await response.json(),
                          )
                        } catch (error) {
                          console.error('Test webhook error:', error)
                        }
                      }}
                      className="rounded bg-red-500 px-3 py-1 font-mono text-xs text-white hover:bg-red-600"
                    >
                      Test FAILED Webhook
                    </button>
                  </div>
                </div>
              )}

            {/* Payment Method Selection */}
            <div className="space-y-4">
              <div
                className={`flex items-center gap-2 ${theme === 'dark' ? '' : 'bg-white'}`}
              >
                <h2
                  className={`font-mono text-sm ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'}`}
                >
                  Select Payment Method
                </h2>
              </div>

              <p className="font-mono text-xs text-gray-500 dark:text-gray-400">
                Selected: {selectedMethod.title}
              </p>

              <RadioGroup
                value={selectedMethod}
                onChange={setSelectedMethod}
                disabled={isProcessing}
              >
                <div className="space-y-6">
                  {/* Indonesian Payment Methods */}
                  <div>
                    <h3 className="mb-3 font-mono text-xs font-bold uppercase tracking-wider text-gray-600 dark:text-gray-400">
                      🇮🇩 Indonesian Payment Methods
                    </h3>
                    <div className="space-y-3">
                      {paymentMethods
                        .filter((method) => method.category === 'indonesian')
                        .map((method) => (
                          <RadioGroup.Option
                            key={method.id}
                            value={method}
                            disabled={isProcessing}
                            className={({ checked }) =>
                              `relative overflow-hidden rounded border ${
                                isProcessing
                                  ? 'cursor-not-allowed bg-gray-100 opacity-50 dark:bg-gray-800'
                                  : 'cursor-pointer'
                              } ${
                                checked && !isProcessing
                                  ? `${theme === 'dark' ? 'border-blue-500/50 bg-blue-500/10' : 'border-blue-500/50 bg-blue-500/10'}`
                                  : !isProcessing
                                    ? `${theme === 'dark' ? 'border-blue-900/20 bg-[#151821]/50 hover:border-blue-500/30 hover:bg-blue-500/5' : 'border-gray-200 bg-white hover:border-blue-500/30 hover:bg-blue-500/5'}`
                                    : 'border-gray-300 dark:border-gray-600'
                              } px-5 py-4 transition-all`
                            }
                          >
                            {({ checked }) => (
                              <>
                                <div className="flex items-center gap-4">
                                  <method.icon
                                    className={`h-5 w-5 ${checked ? 'text-blue-400' : 'text-gray-400'}`}
                                  />
                                  <div>
                                    <RadioGroup.Label className="font-mono text-sm text-gray-800 dark:text-gray-300">
                                      {method.title}
                                    </RadioGroup.Label>
                                    <RadioGroup.Description className="font-mono text-xs text-gray-500 dark:text-gray-500">
                                      {method.description}
                                    </RadioGroup.Description>
                                  </div>
                                </div>
                                {checked && (
                                  <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/50 to-transparent" />
                                )}
                              </>
                            )}
                          </RadioGroup.Option>
                        ))}
                    </div>
                  </div>

                  {/* International Payment Methods */}
                  <div>
                    <h3 className="mb-3 font-mono text-xs font-bold uppercase tracking-wider text-gray-600 dark:text-gray-400">
                      🌍 International Payment Methods
                    </h3>
                    <div className="space-y-3">
                      {paymentMethods
                        .filter((method) => method.category === 'international')
                        .map((method) => (
                          <RadioGroup.Option
                            key={method.id}
                            value={method}
                            disabled={isProcessing}
                            className={({ checked }) =>
                              `relative overflow-hidden rounded border ${
                                isProcessing
                                  ? 'cursor-not-allowed bg-gray-100 opacity-50 dark:bg-gray-800'
                                  : 'cursor-pointer'
                              } ${
                                checked && !isProcessing
                                  ? `${theme === 'dark' ? 'border-blue-500/50 bg-blue-500/10' : 'border-blue-500/50 bg-blue-500/10'}`
                                  : !isProcessing
                                    ? `${theme === 'dark' ? 'border-blue-900/20 bg-[#151821]/50 hover:border-blue-500/30 hover:bg-blue-500/5' : 'border-gray-200 bg-white hover:border-blue-500/30 hover:bg-blue-500/5'}`
                                    : 'border-gray-300 dark:border-gray-600'
                              } px-5 py-4 transition-all`
                            }
                          >
                            {({ checked }) => (
                              <>
                                <div className="flex items-center gap-4">
                                  <method.icon
                                    className={`h-5 w-5 ${checked ? 'text-blue-400' : 'text-gray-400'}`}
                                  />
                                  <div>
                                    <RadioGroup.Label className="font-mono text-sm text-gray-800 dark:text-gray-300">
                                      {method.title}
                                    </RadioGroup.Label>
                                    <RadioGroup.Description className="font-mono text-xs text-gray-500 dark:text-gray-500">
                                      {method.description}
                                    </RadioGroup.Description>
                                  </div>
                                </div>
                                {checked && (
                                  <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/50 to-transparent" />
                                )}
                              </>
                            )}
                          </RadioGroup.Option>
                        ))}
                    </div>
                  </div>
                </div>
              </RadioGroup>
            </div>

            {/* Credit Card Form */}
            {selectedMethod.id === 'credit-card' && (
              <div className="space-y-4">
                <div
                  className={`flex items-center gap-2 ${theme === 'dark' ? '' : 'bg-white'}`}
                >
                  <h2
                    className={`font-mono text-sm ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'}`}
                  >
                    Encrypted Card Details
                  </h2>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="font-mono text-sm text-gray-600 dark:text-gray-400">
                      Card Number
                    </label>
                    <input
                      type="text"
                      className="mt-2 block w-full rounded border border-gray-200 bg-gray-50 px-3 py-2 font-mono text-sm text-gray-800 placeholder:text-gray-400 focus:border-blue-500/50 focus:ring-1 focus:ring-blue-500/20 dark:border-white/5 dark:bg-[#151821] dark:text-gray-300 dark:placeholder:text-gray-600"
                      placeholder="4242 4242 4242 4242"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="font-mono text-sm text-gray-600 dark:text-gray-400">
                        Expiration Date
                      </label>
                      <input
                        type="text"
                        className="mt-2 block w-full rounded border border-gray-200 bg-gray-50 px-3 py-2 font-mono text-sm text-gray-800 placeholder:text-gray-400 focus:border-blue-500/50 focus:ring-1 focus:ring-blue-500/20 dark:border-white/5 dark:bg-[#151821] dark:text-gray-300 dark:placeholder:text-gray-600"
                        placeholder="MM/YY"
                      />
                    </div>
                    <div>
                      <label className="font-mono text-sm text-gray-600 dark:text-gray-400">
                        Security Code
                      </label>
                      <input
                        type="text"
                        className="mt-2 block w-full rounded border border-gray-200 bg-gray-50 px-3 py-2 font-mono text-sm text-gray-800 placeholder:text-gray-400 focus:border-blue-500/50 focus:ring-1 focus:ring-blue-500/20 dark:border-white/5 dark:bg-[#151821] dark:text-gray-300 dark:placeholder:text-gray-600"
                        placeholder="123"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* PayPal */}
            {selectedMethod.id === 'paypal' && (
              <div className="space-y-4">
                <div
                  className={`flex items-center gap-2 ${theme === 'dark' ? '' : 'bg-white'}`}
                >
                  <h2
                    className={`font-mono text-sm ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'}`}
                  >
                    PayPal Checkout
                  </h2>
                </div>

                <div className="rounded border border-gray-200 bg-gray-50 p-4 dark:border-white/5 dark:bg-[#151821]/50">
                  <p className="mb-4 font-mono text-xs text-gray-600 dark:text-gray-400">
                    You will be redirected to PayPal's secure checkout page to
                    complete your payment.
                  </p>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="font-mono text-xs text-gray-500 dark:text-gray-400">
                        Payment Method:
                      </span>
                      <span className="font-mono text-xs text-gray-800 dark:text-gray-300">
                        PayPal Account or Card
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-mono text-xs text-gray-500 dark:text-gray-400">
                        Processing Time:
                      </span>
                      <span className="font-mono text-xs text-gray-800 dark:text-gray-300">
                        Instant
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Apple Pay */}
            {selectedMethod.id === 'apple-pay' && (
              <div className="space-y-4">
                <div
                  className={`flex items-center gap-2 ${theme === 'dark' ? '' : 'bg-white'}`}
                >
                  <h2
                    className={`font-mono text-sm ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'}`}
                  >
                    Apple Pay
                  </h2>
                </div>

                <div className="rounded border border-gray-200 bg-gray-50 p-4 dark:border-white/5 dark:bg-[#151821]/50">
                  <p className="mb-4 font-mono text-xs text-gray-600 dark:text-gray-400">
                    Use Touch ID, Face ID, or your device passcode to complete
                    your purchase securely.
                  </p>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="font-mono text-xs text-gray-500 dark:text-gray-400">
                        Device Authentication:
                      </span>
                      <span className="font-mono text-xs text-gray-800 dark:text-gray-300">
                        Biometric or Passcode
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-mono text-xs text-gray-500 dark:text-gray-400">
                        Processing Time:
                      </span>
                      <span className="font-mono text-xs text-gray-800 dark:text-gray-300">
                        Instant
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Google Pay */}
            {selectedMethod.id === 'google-pay' && (
              <div className="space-y-4">
                <div
                  className={`flex items-center gap-2 ${theme === 'dark' ? '' : 'bg-white'}`}
                >
                  <h2
                    className={`font-mono text-sm ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'}`}
                  >
                    Google Pay
                  </h2>
                </div>

                <div className="rounded border border-gray-200 bg-gray-50 p-4 dark:border-white/5 dark:bg-[#151821]/50">
                  <p className="mb-4 font-mono text-xs text-gray-600 dark:text-gray-400">
                    Complete your purchase using your saved Google Pay payment
                    methods.
                  </p>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="font-mono text-xs text-gray-500 dark:text-gray-400">
                        Authentication:
                      </span>
                      <span className="font-mono text-xs text-gray-800 dark:text-gray-300">
                        Google Account
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-mono text-xs text-gray-500 dark:text-gray-400">
                        Processing Time:
                      </span>
                      <span className="font-mono text-xs text-gray-800 dark:text-gray-300">
                        Instant
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Cryptocurrency */}
            {selectedMethod.id === 'cryptocurrency' && (
              <div className="space-y-4">
                <div
                  className={`flex items-center gap-2 ${theme === 'dark' ? '' : 'bg-white'}`}
                >
                  <h2
                    className={`font-mono text-sm ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'}`}
                  >
                    Cryptocurrency Payment
                  </h2>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="font-mono text-sm text-gray-600 dark:text-gray-400">
                      Select Cryptocurrency
                    </label>
                    <select className="mt-2 block w-full rounded border border-gray-200 bg-gray-50 px-3 py-2 font-mono text-sm text-gray-800 focus:border-blue-500/50 focus:ring-1 focus:ring-blue-500/20 dark:border-white/5 dark:bg-[#151821] dark:text-gray-300">
                      <option value="bitcoin">Bitcoin (BTC)</option>
                      <option value="ethereum">Ethereum (ETH)</option>
                      <option value="litecoin">Litecoin (LTC)</option>
                      <option value="usdc">USD Coin (USDC)</option>
                    </select>
                  </div>

                  <div className="rounded border border-gray-200 bg-gray-50 p-4 dark:border-white/5 dark:bg-[#151821]/50">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="font-mono text-xs text-gray-500 dark:text-gray-400">
                          Network Fee:
                        </span>
                        <span className="font-mono text-xs text-gray-800 dark:text-gray-300">
                          Included
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-mono text-xs text-gray-500 dark:text-gray-400">
                          Processing Time:
                        </span>
                        <span className="font-mono text-xs text-gray-800 dark:text-gray-300">
                          5-15 minutes
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-mono text-xs text-gray-500 dark:text-gray-400">
                          Payment Address:
                        </span>
                        <span className="font-mono text-xs text-blue-600 dark:text-blue-400">
                          Generated after confirmation
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Bank Transfer Details */}
            {selectedMethod.id === 'bank-transfer' && (
              <div className="space-y-4">
                <div
                  className={`flex items-center gap-2 ${theme === 'dark' ? '' : 'bg-white'}`}
                >
                  <h2
                    className={`font-mono text-sm ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'}`}
                  >
                    Secure Transfer Instructions
                  </h2>
                </div>

                <div className="rounded border border-gray-200 bg-gray-50 p-4 dark:border-white/5 dark:bg-[#151821]/50">
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="font-mono text-xs text-gray-500 dark:text-gray-400">
                        Financial Institution:
                      </span>
                      <span className="font-mono text-xs text-gray-800 dark:text-gray-300">
                        SuperSense Financial
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-mono text-xs text-gray-500 dark:text-gray-400">
                        Account Number:
                      </span>
                      <span className="font-mono text-xs text-gray-800 dark:text-gray-300">
                        **********
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-mono text-xs text-gray-500 dark:text-gray-400">
                        Routing Number:
                      </span>
                      <span className="font-mono text-xs text-gray-800 dark:text-gray-300">
                        *********
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-mono text-xs text-gray-500 dark:text-gray-400">
                        Transaction Reference:
                      </span>
                      <span className="font-mono text-xs text-blue-600 dark:text-blue-400">
                        SS-
                        {Math.random()
                          .toString(36)
                          .substring(2, 10)
                          .toUpperCase()}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="rounded border border-gray-200 bg-gray-50 p-4 dark:border-white/5 dark:bg-[#151821]/50">
                  <p className="font-mono text-xs text-gray-600 dark:text-gray-400">
                    Please include your unique transaction reference code with
                    your bank transfer. Your SuperSense subscription will be
                    activated within 1-2 business days after payment
                    verification. For enterprise-level expedited processing,
                    please contact our dedicated support team.
                  </p>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-end pt-6">
              <button
                type="submit"
                onClick={handleSubmit}
                disabled={isProcessing}
                className={`flex items-center gap-2 rounded px-4 py-2 font-mono text-sm transition-colors ${
                  isProcessing
                    ? 'cursor-not-allowed bg-gray-400 text-white'
                    : 'bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-500/20 dark:text-blue-400 dark:hover:bg-blue-500/30'
                }`}
              >
                {isProcessing ? (
                  <>
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                    Processing...
                  </>
                ) : paymentStatus === 'success' ? (
                  <>
                    <svg
                      className="h-4 w-4"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Payment Successful!
                  </>
                ) : plan && currentPlanPricing ? (
                  <>
                    {paymentType === 'subscription' ? 'Subscribe' : 'Purchase'}{' '}
                    for $
                    {paymentType === 'subscription'
                      ? currentPlanPricing.subscription.price
                      : currentPlanPricing.purchase.price}
                    {paymentType === 'subscription' &&
                      ` ${currentPlanPricing.subscription.duration}`}
                  </>
                ) : plan ? (
                  'Secure and Finalize Payment'
                ) : (
                  'Save Payment Method'
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Subtle scanline effect */}
        <div className="pointer-events-none absolute inset-0 bg-[linear-gradient(to_bottom,rgba(59,130,246,0.02)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
      </div>

      {/* Global scanline effect */}
      <div className="pointer-events-none fixed inset-0 bg-[linear-gradient(to_bottom,rgba(255,255,255,0.03)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
    </div>
  )
}
