// lib/services/userService.js

export const userService = {
  // Get current user dashboard data
  getCurrentUser: async () => {
    const response = await fetch('/api/dashboard', {
      method: 'GET',
      credentials: 'include', // Include cookies
      headers: {
        'Accept': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch user data: ${response.status}`)
    }

    const result = await response.json()
    
    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch user data')
    }

    // Transform backend data to match frontend expectations
    return transformDashboardData(result.data)
  },

  // Get dashboard data specifically
  getDashboardData: async () => {
    const response = await fetch('/api/dashboard', {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Accept': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch dashboard data: ${response.status}`)
    }

    const result = await response.json()
    
    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch dashboard data')
    }

    return result.data
  }
}

// Transform backend data structure to match frontend component expectations
function transformDashboardData(backendData) {
  const { user, stats, websites, tierInfo } = backendData

  return {
    // User info
    id: user.id,
    email: user.email,
    username: user.profile.username,
    displayName: user.profile.displayName,
    type: user.type,
    createdAt: user.createdAt,

    // Account details for QuickStats component
    accountDetails: {
      limits: {
        currentUsage: {
          websites: stats.websites.current,
        },
        restrictions: {
          websiteLimit: stats.websites.max,
          creditLimit: stats.credits.max,
        }
      },
      subscription: {
        currentPlan: stats.currentTier.toUpperCase(),
      }
    },

    // Metrics for QuickStats component
    metrics: {
      totalCreditsUsed: stats.credits.current,
      totalScans: stats.posts,
      totalUsage: stats.totalUsage,
    },

    // Websites data
    websites: websites.map(website => ({
      domain: website.domain,
      status: website.status,
      // Fix credits display: used/total format
      used: website.creditsUsed || 0, // Credits yang sudah digunakan
      credit: stats.credits.max || 100, // Total credit limit dari tier (shared across websites)
      creditsUsed: website.creditsUsed,
      lastScanned: website.lastScan, // Match WebsitesTable expectation
      key: website.apiKey, // Match WebsitesTable expectation
      apiKey: website.apiKey,
      totalScans: website.totalScans,
      // Add scanHistory for activity page
      scanHistory: website.scanHistory || (website.lastScan ? [{
        id: `scan_${website.domain}_${Date.now()}`,
        date: website.lastScan,
        status: 'completed',
        scansCount: website.totalScans || 0,
        type: 'auto_scan',
        domain: website.domain
      }] : [])
    })),

    // Tier information
    tierInfo: {
      tier: tierInfo.tier,
      tierName: tierInfo.tierName,
      creditLimit: tierInfo.creditLimit,
      websiteLimit: tierInfo.websiteLimit,
      price: tierInfo.price,
      features: tierInfo.features,
    },

    // Stats summary
    stats: {
      websites: stats.websites,
      credits: stats.credits,
      currentTier: stats.currentTier,
      posts: stats.posts,
      totalUsage: stats.totalUsage,
    },

    // Raw backend data for any custom usage
    _raw: backendData,
  }
}
