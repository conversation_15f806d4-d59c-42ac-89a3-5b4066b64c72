'use client'

import { useUserContext } from '@/contexts/UserProvider'
import {
  QuickStats,
  RecentActivityTable,
  DashboardSkeleton,
} from '@/components/dashboard/pages/dashboard'

export default function ActivityPage() {
  const { user, isLoading } = useUserContext()

  if (isLoading) {
    return <DashboardSkeleton />
  }

  // Handle missing scanHistory data
  const recentScans = user.websites
    .filter(website => website.scanHistory && Array.isArray(website.scanHistory))
    .flatMap((website) =>
      website.scanHistory.map((scan) => ({
        ...scan,
        domain: website.domain,
      })),
    )
    .sort((a, b) => new Date(b.date) - new Date(a.date))

  // If no scan history available, create mock data or empty array
  const fallbackScans = user.websites.length > 0 ? user.websites.map(website => ({
    id: `scan_${website.domain}_${Date.now()}`,
    domain: website.domain,
    date: website.lastScanned || new Date().toISOString(),
    status: website.status === 'active' ? 'completed' : 'pending',
    scansCount: website.totalScans || 0,
    type: 'auto_scan'
  })) : []

  const activityData = recentScans.length > 0 ? recentScans : fallbackScans

  return (
    <div className="space-y-6">
      <QuickStats user={user} />

      <div className="relative overflow-hidden rounded-md bg-gradient-to-b from-[#0f1117] to-[#151821] border border-gray-100 dark:border-gray-900">
        {/* Ambient glow effect */}
        <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent" />

        {/* Header */}
        <div className="border-b border-gray-100 bg-white px-6 py-4 dark:border-blue-900/20 dark:bg-[#151821]/50">
          <div className="flex items-center gap-2">
            <h2 className="font-mono text-sm text-blue-400">Activity Logs</h2>
          </div>
          <p className="mt-2 font-mono text-xs text-gray-500">
            <span className="text-gray-400">Total Scans:</span>{' '}
            <span className="text-blue-400">{activityData.length}</span> |{' '}
            <span className="text-gray-400">Domains:</span>{' '}
            <span className="text-blue-400">
              {new Set(activityData.map((scan) => scan.domain)).size}
            </span>
          </p>
        </div>

        {/* Table */}
        <RecentActivityTable scanHistory={activityData} />

        {/* Subtle scanline effect */}
        <div className="pointer-events-none absolute inset-0 bg-[linear-gradient(to_bottom,rgba(59,130,246,0.02)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
      </div>

      {/* Global scanline effect */}
      <div className="pointer-events-none fixed inset-0 bg-[linear-gradient(to_bottom,rgba(255,255,255,0.03)_50%,transparent_50%)] bg-[length:100%_4px] opacity-20" />
    </div>
  )
}
